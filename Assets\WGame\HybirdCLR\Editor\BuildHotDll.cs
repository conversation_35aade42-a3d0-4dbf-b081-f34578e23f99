using System.Collections.Generic;
using System.IO;
using System;
using HybridCLR.Editor;
using HybridCLR.Editor.HotUpdate;
using UnityEditor;
using UnityEngine;

public class BuildHotDll
{
    [MenuItem("Build/BuildBaseDll")]
    public static void BuildBaseDll()
    {
        // 如果是Android平台，清空yoo目录
        ClearAndroidYooDirectory();

        UpdateResVersion(1);
        Obfuz4HybridCLR.PrebuildCommandExt.GenerateAll();
        CopyAOTAssembliesToStreamingAssets();
        CopyHotUpdateAssembliesToStreamingAssets();
        CopyObfuscateMapToRelease();
        Debug.Log("[BuildHotDll] 构建完成");

        // 刷新资源数据库
        AssetDatabase.Refresh();
    }


    [MenuItem("Build/BuildHotDll")]
    public static void BuildDll()
    {
        ClearAndroidYooDirectory();

        ResVersion.version++;
        UpdateResVersion(ResVersion.version);
        Obfuz4HybridCLR.PrebuildCommandExt.CompileAndObfuscateDll();
        CopyHotUpdateAssembliesToStreamingAssets();
        CopyObfuscateMapToRelease();
        Debug.Log("[BuildHotDll] 构建完成");

        AssetDatabase.Refresh();
    }

    private static void CopyObfuscateMapToRelease()
    {
        string sourcePath = "Assets/Obfuz/SymbolObfus/symbol-mapping.xml";

        if (!File.Exists(sourcePath))
        {
            Debug.LogWarning($"[CopyObfuscateMapToRelease] 源文件不存在: {sourcePath}");
            return;
        }

        // 获取当前构建平台
        var target = EditorUserBuildSettings.activeBuildTarget;
        string platformName = target == BuildTarget.Android ? "apk" :
                             target == BuildTarget.iOS ? "ios" :
                             target.ToString().ToLower();

        // 获取版本信息
        string gameVersion = GameConfig.GetVer();
        int resVersion = ResVersion.version;

        // 构建目标文件名
        string fileName = $"symbol-mapping-{platformName}-{gameVersion}.{resVersion}.xml";

        // 确保目标目录存在
        string targetDir = "Release/ObfuscateMap";
        if (!Directory.Exists(targetDir))
        {
            Directory.CreateDirectory(targetDir);
        }

        string targetPath = Path.Combine(targetDir, fileName);

        try
        {
            File.Copy(sourcePath, targetPath, true);
            Debug.Log($"[CopyObfuscateMapToRelease] 混淆映射文件已拷贝: {sourcePath} -> {targetPath}");
        }
        catch (Exception e)
        {
            Debug.LogError($"[CopyObfuscateMapToRelease] 拷贝混淆映射文件失败: {e.Message}");
        }
    }

    private static void UpdateResVersion(int ver)
    {
        ResVersion.version = ver;
        string content = "public class ResVersion\n{\n" + "    public static int version = " + ResVersion.version + ";\n}";
        File.WriteAllText("Assets/_MyGame/Scripts/ResVersion.cs", content);
    }

    private static void CompileDll()
    {
        // HybridCLR.Editor.Commands.CompileDllCommand.CompileDllActiveBuildTarget();//未混淆版本
        Obfuz4HybridCLR.PrebuildCommandExt.CompileAndObfuscateDll();
    }

    private static string GetObfuscatedHotUpdateAssemblyOutputPath(BuildTarget target)
    {
        return $"{Obfuz.Settings.ObfuzSettings.Instance.ObfuzRootDir}/{target}/ObfuscatedHotUpdateAssemblies";
    }

    public static void CopyAOTAssembliesToStreamingAssets()
    {
        var target = EditorUserBuildSettings.activeBuildTarget;
        string aotAssembliesSrcDir = SettingsUtil.GetAssembliesPostIl2CppStripDir(target);
        string aotAssembliesDstDir = Path.Combine(Application.dataPath, "..", "Assets", "_MyGame", "Bundles", "AOT", target.ToString());

        if (!Directory.Exists(aotAssembliesDstDir))
        {
            Directory.CreateDirectory(aotAssembliesDstDir);
        }

        foreach (var dll in AOTGenericReferences.PatchedAOTAssemblyList)
        {
            string srcDllPath = $"{aotAssembliesSrcDir}/{dll}";
            if (!File.Exists(srcDllPath))
            {
                Debug.LogError($"ab中添加AOT补充元数据dll:{srcDllPath} 时发生错误,文件不存在。裁剪后的AOT dll在BuildPlayer时才能生成，因此需要你先构建一次游戏App后再打包。");
                continue;
            }
            string dllBytesPath = $"{aotAssembliesDstDir}/{dll}.bytes";
            File.Copy(srcDllPath, dllBytesPath, true);
            Debug.Log($"[CopyAOTAssembliesToStreamingAssets] copy AOT dll {srcDllPath} -> {dllBytesPath}");
        }
    }

    public static void CopyHotUpdateAssembliesToStreamingAssets()
    {
        var target = EditorUserBuildSettings.activeBuildTarget;

        // string hotfixDllSrcDir = SettingsUtil.GetHotUpdateDllsOutputDirByTarget(target);//未混淆版本
        string hotfixDllSrcDir = GetObfuscatedHotUpdateAssemblyOutputPath(target);
        string hotfixAssembliesDstDir = Path.Combine(Application.dataPath, "..", "Assets", "_MyGame", "Bundles", "Game", target.ToString());

        if (!Directory.Exists(hotfixAssembliesDstDir))
        {
            Directory.CreateDirectory(hotfixAssembliesDstDir);
        }

        foreach (var dll in SettingsUtil.HotUpdateAssemblyFilesExcludePreserved)
        {
            string dllPath = $"{hotfixDllSrcDir}/{dll}";
            string dllBytesPath = $"{hotfixAssembliesDstDir}/Game.bytes";
            File.Copy(dllPath, dllBytesPath, true);

            Debug.Log($"[CopyHotUpdateAssembliesToStreamingAssets] copy hotfix dll {dllPath} -> {dllBytesPath}");
        }
    }

    /// <summary>
    /// 检查热更新代码中是否引用了被裁剪的类型或函数
    /// </summary>
    [MenuItem("Build/CheckAccessMissingMetadata")]
    public static void CheckAccessMissingMetadata()
    {
        BuildTarget target = EditorUserBuildSettings.activeBuildTarget;
        // aotDir指向 构建主包时生成的裁剪aot dll目录，而不是最新的SettingsUtil.GetAssembliesPostIl2CppStripDir(target)目录。
        // 一般来说，发布热更新包时，由于中间可能调用过generate/all，SettingsUtil.GetAssembliesPostIl2CppStripDir(target)目录中包含了最新的aot dll，
        // 肯定无法检查出类型或者函数裁剪的问题。
        // 需要在构建完主包后，将当时的aot dll保存下来，供后面补充元数据或者裁剪检查。
        string aotDir = SettingsUtil.GetAssembliesPostIl2CppStripDir(target);

        if (!Directory.Exists(aotDir))
        {
            Debug.LogError($"[CheckAccessMissingMetadata] AOT目录不存在: {aotDir}，请先构建主包");
            return;
        }

        // 第2个参数hotUpdateAssNames为热更新程序集列表。对于旗舰版本，该列表需要包含DHE程序集，即SettingsUtil.HotUpdateAndDHEAssemblyNamesIncludePreserved。
        var checker = new MissingMetadataChecker(aotDir, SettingsUtil.HotUpdateAssemblyNamesIncludePreserved);

        // 直接检测Game.bytes文件
        string gameBytesPath = Path.Combine(Application.dataPath, "..", "Assets", "_MyGame", "Bundles", "Game", target.ToString(), "Game.bytes");
        bool hasAnyMissing = false;

        if (!File.Exists(gameBytesPath))
        {
            Debug.LogError($"[CheckAccessMissingMetadata] Game.bytes文件不存在: {gameBytesPath}，请先构建热更新DLL");
            return;
        }

        Debug.Log($"[CheckAccessMissingMetadata] 检查Game.bytes文件: {gameBytesPath}");
        bool notAnyMissing = checker.Check(gameBytesPath);
        if (!notAnyMissing)
        {
            hasAnyMissing = true;
            Debug.LogError($"[CheckAccessMissingMetadata] 检测到缺失的元数据引用: Game.bytes");
        }
        else
        {
            Debug.Log($"[CheckAccessMissingMetadata] 检查通过: Game.bytes");
        }

        if (hasAnyMissing)
        {
            Debug.LogError("[CheckAccessMissingMetadata] 检查完成，发现缺失的元数据引用！请检查上述错误信息。");
            EditorUtility.DisplayDialog("检查结果", "发现缺失的元数据引用！请查看控制台错误信息。", "确定");
        }
        else
        {
            Debug.Log("[CheckAccessMissingMetadata] 检查完成，未发现缺失的元数据引用。");
            EditorUtility.DisplayDialog("检查结果", "检查通过，未发现缺失的元数据引用。", "确定");
        }
    }

    /// <summary>
    /// 清空Android平台的yoo目录
    /// </summary>
    private static void ClearAndroidYooDirectory()
    {
        var target = EditorUserBuildSettings.activeBuildTarget;

        // 只在Android平台执行清空操作
        if (target == BuildTarget.Android)
        {
            string yooDir = "Release/BoBoSurviveClient-TW-AndroidStudio/UnityDataAssetPack/src/main/assets/yoo";

            if (Directory.Exists(yooDir))
            {
                try
                {
                    Directory.Delete(yooDir, true);
                    Debug.Log($"[BuildHotDll] 已清空Android yoo目录: {yooDir}");
                }
                catch (Exception e)
                {
                    Debug.LogError($"[BuildHotDll] 清空Android yoo目录失败: {e.Message}");
                }
            }
            else
            {
                Debug.Log($"[BuildHotDll] Android yoo目录不存在，跳过清空操作: {yooDir}");
            }
        }
    }
}
